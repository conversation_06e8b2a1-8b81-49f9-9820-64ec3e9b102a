[tool.black]
line-length = 100
target-version = ["py311", "py312"]
skip-string-normalization = true

[tool.isort]
profile = "black"
line_length = 100

[build-system]
requires = ["setuptools>=68", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "jetbrains_refresh_token"
version = "0.3.0"
authors = [{ name = "possible055", email = "<EMAIL>" }]
description = ""
readme = "README.md"
license = { text = "MIT" }
requires-python = ">=3.11"

dependencies = [
    "fake_useragent==2.2.0",
    "jsonschema==4.24.0",
    "Requests==2.32.4",
    "setuptools==78.1.1",
    "urllib3==2.5.0",
    "PyJWT==2.10.1",
    "streamlit==1.46.1",
]

keywords = ["python"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Operating System :: OS Independent",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

[project.scripts]
jetbrains_refresh_token = "main:main"
jetbrains_frontend = "jetbrains_refresh_token.frontend.streamlit_app:main"

[project.urls]
Source = "https://github.com/possible055/jetbrains_refresh_token"
Bug-Tracker = "https://github.com/possible055/jetbrains_refresh_token/issues"

[tool.setuptools]
include-package-data = true

[tool.setuptools.packages.find]

where = ["."]

include = ["jetbrains_refresh_token*"]

# exclude = ["tests*"]
