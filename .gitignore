# Python Basic
__pycache__/
*.py[cod]
*$py.class

# Build / Distribution
build/
dist/
*.egg-info/
*.egg

# Virtual Environments
.venv*/
venv*/

# Testing / Coverage
.tox/
.nox/
.pytest_cache/
.coverage*

# Linter / formatter cache
.pylint.d/

# IDE editor settings
.vscode/
.kilocode/

# OS / Logs / Env
.DS_Store
Thumbs.db
*.log
.env
config.json
config-backup.json
jetbrainsai.json
*.db

# --- project-specific rules ---
issues/
